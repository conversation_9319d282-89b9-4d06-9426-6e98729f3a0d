# Instructions (Copilot)

## Where to Find Your Instructions
- Your custom instructions are located in the files inside of the [nam20485/agent-instructions](https://github.com/nam20485/agent-instructions/tree/main) repository
- Look at the files in the `main` branch
- Start with your core instructions (linked below)
- Then follow the links to the other instruction files in that repo as required or needed.
- You will need to follow the links and read the files to understand your instructions

## How to Read Your Instructions
- Read the core instructions first
- Then follow the links from the core instructions to the other instruction files
- Some files are **REQUIRED** and some are **OPTIONAL**
- Files marked **REQUIRED** are ALWAYS active and so must be followed and read
- Otherwise files are optionally active based on user needs and your assigned roles and workflow assignments

## Core Instructions (**REQUIRED**)
[ai-core-instructions.md](https://github.com/nam20485/agent-instructions/blob/main/ai_instruction_modules/ai-core-instructions.md)

## Local AI Instructions (**REQUIRED**)
[ai-local-instructions.md](../local_ai_instructions/ai-local-instructions.md)

### Dynamic Workflow/Assignment/Resolution Guide (**OPTIONAL**)
Consult the [assignment-index.json](../local_ai_instructions/assignment-index.json) file to resolve dynamic workflows and assignments.
- Consult the [ai-resolution-guide.md](../local_ai_instructions/ai-resolution-guide.md) for more information on how to use the index and resolve dynamic workflows and assignments.

End of file.
