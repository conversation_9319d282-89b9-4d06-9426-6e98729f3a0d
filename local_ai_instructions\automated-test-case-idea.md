How about this...  What if we had a way to validate the outputs? For example, we could save the output of a dynamic workflow as a "golden" outtput. And then if we had an "equivalency" fucntion that took as input two output files and compaared them. (You can define equality as litrtal string match of the the output file inputs, or whatever else you think is appropriate). So the input into a compiler ---> output compared to a golden master output = an automated test case functionality. You see? Then we could add a few of our existing dynamic workflows to the CI and have instant validation of commited changes. What do you think?