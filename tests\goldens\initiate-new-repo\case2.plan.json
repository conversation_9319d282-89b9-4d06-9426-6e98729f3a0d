[{"assignment": "initiate-new-repository", "stepId": "create-repo", "description": "Create new repo from nam20485/ai-new-app-template (public, AGPL).", "kind": "gh", "command": "gh repo create sample-repo-2 --public --template nam20485/ai-new-app-template --disable-wiki --enable-issues --license agpl-3.0"}, {"assignment": "initiate-new-repository", "stepId": "copy-docs:Advanced Memory .NET - Dev Plan.md", "description": "Copy app_plan_docs into docs/ in the new repo. (docs\\advanced_memory\\Advanced Memory .NET - Dev Plan.md)", "kind": "local", "command": "Copy-Item -Path docs\\advanced_memory\\Advanced Memory .NET - Dev Plan.md -Destination .\\docs -Force"}, {"assignment": "initiate-new-repository", "stepId": "copy-docs:index.html", "description": "Copy app_plan_docs into docs/ in the new repo. (docs\\advanced_memory\\index.html)", "kind": "local", "command": "Copy-Item -Path docs\\advanced_memory\\index.html -Destination .\\docs -Force"}, {"assignment": "initiate-new-repository", "stepId": "create-project", "description": "Create GitHub Project (Basic Kanban) named {repo_name}.", "kind": "gh", "command": "gh project create --title sample-repo-2 --format json --template basic_kanban"}, {"assignment": "initiate-new-repository", "stepId": "import-labels", "description": "Import labels from .labels.json", "kind": "gh", "command": "pwsh -NoProfile -File scripts/import-labels.ps1 -Owner nam20485 -Repo sample-repo-2 -LabelsFile .labels.json"}, {"assignment": "initiate-new-repository", "stepId": "create-milestones", "description": "Create milestones", "kind": "gh", "command": "pwsh -NoProfile -File scripts/create-milestones.ps1 -Owner nam20485 -Repo sample-repo-2"}, {"assignment": "initiate-new-repository", "stepId": "rename-artifacts", "description": "Rename devcontainer folder and code-workspace filename to include repo name.", "kind": "local", "command": "# rename .devcontainer name and *.code-workspace to sample-repo-2"}]