[{"id": 8775985735, "node_id": "LA_kwDOOpTlG88AAAACCxbqRw", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/assigned", "name": "assigned", "color": "0052cc", "default": false, "description": "copilot"}, {"id": 8849517733, "node_id": "LA_kwDOOpTlG88AAAACD3jspQ", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/assigned:copilot", "name": "assigned:copilot", "color": "ededed", "default": false, "description": null}, {"id": 8606841780, "node_id": "LA_kwDOOpTlG88AAAACAQH7tA", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/bug", "name": "bug", "color": "d73a4a", "default": true, "description": "Something isn't working"}, {"id": 8606841791, "node_id": "LA_kwDOOpTlG88AAAACAQH7vw", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/documentation", "name": "documentation", "color": "0075ca", "default": true, "description": "Improvements or additions to documentation"}, {"id": 8606841799, "node_id": "LA_kwDOOpTlG88AAAACAQH7xw", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/duplicate", "name": "duplicate", "color": "cfd3d7", "default": true, "description": "This issue or pull request already exists"}, {"id": 8606841807, "node_id": "LA_kwDOOpTlG88AAAACAQH7zw", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/enhancement", "name": "enhancement", "color": "a2eeef", "default": true, "description": "New feature or request"}, {"id": 8606841817, "node_id": "LA_kwDOOpTlG88AAAACAQH72Q", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/good%20first%20issue", "name": "good first issue", "color": "7057ff", "default": true, "description": "Good for newcomers"}, {"id": 8606841810, "node_id": "LA_kwDOOpTlG88AAAACAQH70g", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/help%20wanted", "name": "help wanted", "color": "008672", "default": true, "description": "Extra attention is needed"}, {"id": 8606841820, "node_id": "LA_kwDOOpTlG88AAAACAQH73A", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/invalid", "name": "invalid", "color": "e4e669", "default": true, "description": "This doesn't seem right"}, {"id": 8606841826, "node_id": "LA_kwDOOpTlG88AAAACAQH74g", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/question", "name": "question", "color": "d876e3", "default": true, "description": "Further information is requested"}, {"id": 8776078411, "node_id": "LA_kwDOOpTlG88AAAACCxhUSw", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/state", "name": "state", "color": "b17457", "default": false, "description": "blocked"}, {"id": 8849517735, "node_id": "LA_kwDOOpTlG88AAAACD3jspw", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/state:in-progress", "name": "state:in-progress", "color": "ededed", "default": false, "description": null}, {"id": 8849557410, "node_id": "LA_kwDOOpTlG88AAAACD3mHog", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/state:planning", "name": "state:planning", "color": "ededed", "default": false, "description": null}, {"id": 8849557406, "node_id": "LA_kwDOOpTlG88AAAACD3mHng", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/type:enhancement", "name": "type:enhancement", "color": "ededed", "default": false, "description": null}, {"id": 8606841835, "node_id": "LA_kwDOOpTlG88AAAACAQH76w", "url": "https://api.github.com/repos/nam20485/AgentAsAService/labels/wontfix", "name": "wontfix", "color": "ffffff", "default": true, "description": "This will not be worked on"}]